<?php

$item_classes = $this_object->getItemClasses($params);
$params['title_styles'] = $this_object->getTitleStyles($params);
$params['image_size'] = 'full';
?>
<div class="mkd-pli <?php echo esc_attr($item_classes); ?>">
    <div class="mkd-pla-inner">

        <div class="mkd-pla-inner-info">
            <div class="mkd-pla-image">
                <div class="mkd-pla-image-inner">
                    <?php cyberstore_mikado_get_module_template_part('templates/parts/image', 'woocommerce', '', $params); ?>
                </div>
                <a class="mkd-pli-link" itemprop="url" href="<?php the_permalink(); ?>"
                   title="<?php the_title_attribute(); ?>"></a>
            </div>


            <div
                class="mkd-pla-text-wrapper">

                <?php

                cyberstore_mikado_get_module_template_part('templates/parts/labels', 'woocommerce', '', $params);

                cyberstore_mikado_get_module_template_part('templates/parts/category', 'woocommerce', '', $params);

                cyberstore_mikado_get_module_template_part('templates/parts/title', 'woocommerce', '', $params);

                cyberstore_mikado_get_module_template_part('templates/parts/rating', 'woocommerce', '', $params);

                cyberstore_mikado_get_module_template_part('templates/parts/price', 'woocommerce', '', $params);

                ?>
            </div>
        </div>

        <div class="mkd-pla-cv-holder">

            <?php

            cyberstore_mikado_get_module_template_part('templates/parts/compare', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/wishlist', 'woocommerce', '', $params);

            ?>

        </div>

        <?php

        cyberstore_mikado_get_module_template_part('templates/parts/add-to-cart', 'woocommerce', '', $params);

        ?>

    </div>
</div>