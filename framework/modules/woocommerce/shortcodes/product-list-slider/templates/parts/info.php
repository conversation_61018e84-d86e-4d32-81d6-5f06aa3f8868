<?php

$params['title_styles'] = $this_object->getTitleStyles($params);
?>
<div class="mkd-plsli">
    <div class="mkd-plsli-holder">
        <?php
        cyberstore_mikado_get_module_template_part('templates/parts/category', 'woocommerce', '', $params); ?>

        <div class="mkd-compare-wishlist-wrapper">
            <?php
            cyberstore_mikado_get_module_template_part('templates/parts/compare', 'woocommerce', '', $params);
            cyberstore_mikado_get_module_template_part('templates/parts/wishlist', 'woocommerce', '', $params);
            ?>
        </div>

        <div class="mkd-plsli-inner">
            <div class="mkd-plsli-image">
                <?php cyberstore_mikado_get_module_template_part('templates/parts/labels', 'woocommerce', '', $params); ?>
                <?php cyberstore_mikado_get_module_template_part('templates/parts/image', 'woocommerce', '', $params); ?>
            </div>
            <a class="mkd-plsli-link" itemprop="url" href="<?php the_permalink(); ?>"
               title="<?php the_title_attribute(); ?>"></a>
        </div>

        <div class="mkd-plsli-text-wrapper">

            <div class="mkd-plsli-text-left-holder">
                <?php cyberstore_mikado_get_module_template_part('templates/parts/rating', 'woocommerce', '', $params); ?>

                <?php cyberstore_mikado_get_module_template_part('templates/parts/title', 'woocommerce', '', $params); ?>

                <?php cyberstore_mikado_get_module_template_part('templates/parts/price', 'woocommerce', '', $params); ?>
            </div>

            <?php cyberstore_mikado_get_module_template_part('templates/parts/add-to-cart', 'woocommerce', '', $params); ?>

        </div>
    </div>
</div>