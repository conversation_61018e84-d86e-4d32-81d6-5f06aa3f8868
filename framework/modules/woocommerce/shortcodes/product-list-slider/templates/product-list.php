<?php
$shader_styles = $this_object->getShaderStyles($params);
$params['title_styles'] = $this_object->getTitleStyles($params);
?>
<div class="mkd-plsl-holder <?php echo esc_attr($holder_classes) ?>">
    <div class="mkd-plsl-outer mkd-owl-slider" <?php echo cyberstore_mikado_get_inline_attrs($holder_data); ?>>
        <?php if ($query_result->have_posts()): while ($query_result->have_posts()) : $query_result->the_post();
            echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/info', 'product-list-slider', '', $params); endwhile;
        else:
            cyberstore_mikado_get_module_template_part('templates/parts/no-posts', 'woocommerce', '', $params);
        endif;
        wp_reset_postdata();
        ?>
    </div>
</div>