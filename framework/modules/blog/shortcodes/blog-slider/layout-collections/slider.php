<li class="mkd-blog-slider-item">
    <div class="mkd-blog-slider-item-inner">
        <div class="mkd-item-image">
            <?php
            if ($post_info_date == 'yes') {
            cyberstore_mikado_get_module_template_part('templates/parts/post-info/date-on-image', 'blog', '', $params);
            }
            ?>
            <a itemprop="url" href="<?php echo get_permalink(); ?>">
                <?php echo get_the_post_thumbnail(get_the_ID(), $image_size); ?>
            </a>
        </div>
        <div class="mkd-item-text-wrapper">
            <div class="mkd-item-text-holder">
                <div class="mkd-item-text-holder-inner">
	                <?php if($post_info_date == 'yes' || $post_info_category == 'yes' || $post_info_author == 'yes' || $post_info_comments == 'yes'){ ?>
		                <div class="mkd-item-info-section">
			                <?php
			                if ($post_info_category == 'yes') {
				                cyberstore_mikado_get_module_template_part('templates/parts/post-info/category', 'blog', '', $params);
			                }
			                if ($post_info_author == 'yes') {
				                cyberstore_mikado_get_module_template_part('templates/parts/post-info/author', 'blog', '', $params);
			                }
			                if ($post_info_comments == 'yes') {
				                cyberstore_mikado_get_module_template_part('templates/parts/post-info/comments', 'blog', '', $params);
			                }
			                ?>
		                </div>
	                <?php } ?>
	
	                <?php cyberstore_mikado_get_module_template_part('templates/parts/title', 'blog', '', $params); ?>

                    <?php cyberstore_mikado_get_module_template_part('templates/parts/excerpt', 'blog', '', $params); ?>
                </div>
            </div>
        </div>
    </div>
</li>