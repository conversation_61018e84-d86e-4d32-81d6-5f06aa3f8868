<?php
$month = get_the_time('m');
$year = get_the_time('Y');
$title = get_the_title();

$post_id = get_the_ID();
if ( has_post_thumbnail($post_id) ) {

?>

<div itemprop="dateCreated" class="mkd-post-info-date-on-image entry-date published updated">
    <?php if (empty($title) && cyberstore_mikado_blog_item_has_link()) { ?>
    <a itemprop="url" href="<?php the_permalink() ?>">
        <?php } else { ?>

        <a itemprop="url" href="<?php echo get_month_link($year, $month); ?>">
            <?php } ?>

            <div class="mkd-post-info-date-day"><?php the_time('j'); ?> </div>
            <div class="mkd-post-info-date-month"><?php the_time('M'); ?> </div>
        </a>
        <meta itemprop="interactionCount"
              content="UserComments: <?php echo get_comments_number(cyberstore_mikado_get_page_id()); ?>"/>
</div>

<?php } ?>