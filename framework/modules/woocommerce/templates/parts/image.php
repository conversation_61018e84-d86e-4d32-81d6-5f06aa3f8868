<?php

$product = cyberstore_mikado_return_woocommerce_global_variable();

$product_id = $product->get_id();

?>

<?php if (!$product->is_in_stock()) { ?>
    <span
        class="mkd-<?php echo esc_attr($class_name); ?>-out-of-stock"><?php esc_html_e('Out of Stock', 'cyberstore'); ?></span>
<?php }

$product_image_size = 'woocommerce_thumbnail';
if ($image_size === 'full') {
    $product_image_size = 'full';
} else if ($image_size === 'square') {
    $product_image_size = 'cyberstore_square';
} else if ($image_size === 'landscape') {
    $product_image_size = 'cyberstore_landscape';
} else if ($image_size === 'portrait') {
    $product_image_size = 'cyberstore_portrait';
} else if ($image_size === 'medium') {
    $product_image_size = 'medium';
} else if ($image_size === 'large') {
    $product_image_size = 'large';
} else if ($image_size === 'woocommerce_gallery_thumbnail') {
    $product_image_size = 'woocommerce_gallery_thumbnail';
}

if (has_post_thumbnail()) {
    the_post_thumbnail(apply_filters('cyberstore_mikado_product_list_image_size', $product_image_size));
}