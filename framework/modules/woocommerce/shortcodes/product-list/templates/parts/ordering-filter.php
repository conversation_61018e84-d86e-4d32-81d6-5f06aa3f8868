<?php if ($show_ordering_filter == 'yes') {

    if ($filters_position == 'top') { ?>
        <div class="mkd-pl-order-filter-holder">
        <h6><?php esc_html_e('Filter', 'cyberstore'); ?></h6>
    <?php } ?>
    <div class="mkd-pl-ordering-outer">
        <div class="mkd-pl-ordering">
            <div class="mkd-pl-sort-filter">
                <h5><?php esc_html_e('Sort By', 'cyberstore'); ?></h5>
                <ul>
                    <?php echo cyberstore_mikado_get_module_part($ordering_filter_list); ?>
                </ul>
            </div>
            <div class="mkd-pl-range-filter">
                <h5><?php esc_html_e('Price Range', 'cyberstore'); ?></h5>
                <ul class="mkd-pl-ordering-price">
                    <?php echo cyberstore_mikado_get_module_part($pricing_filter_list); ?>
                </ul>
            </div>
        </div>
    </div>

    <?php if ($filters_position == 'top') { ?>
        </div>
    <?php }
} ?>