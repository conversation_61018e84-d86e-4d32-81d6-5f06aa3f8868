<?php if ($query_result->have_posts()): while ($query_result->have_posts()) : $query_result->the_post(); ?>
    <div class="mkd-pvd mkd-pvd-middle clearfix <?php echo esc_attr($product_vd_border); ?>">
        <?php

        cyberstore_mikado_get_module_template_part('templates/parts/category', 'woocommerce', '', $params);

        ?>
         <div class="mkd-pvd-compare">
             <?php
                cyberstore_mikado_get_module_template_part('templates/parts/compare', 'woocommerce', '', $params);

                cyberstore_mikado_get_module_template_part('templates/parts/wishlist', 'woocommerce', '', $params);
            ?>
         </div>

        <?php
            cyberstore_mikado_get_module_template_part('templates/parts/value-deal', 'woocommerce', '', $params);
        ?>

        <div class="mkd-pli-inner">
            <div class="mkd-pli-image">
                <?php cyberstore_mikado_get_module_template_part('templates/parts/image', 'woocommerce', '', $params); ?>
            </div>
            <a class="mkd-pli-link" itemprop="url" href="<?php the_permalink(); ?>"
               title="<?php the_title_attribute(); ?>"></a>
        </div>

        <?php

        cyberstore_mikado_get_module_template_part('templates/parts/title', 'woocommerce', '', $params);

        cyberstore_mikado_get_module_template_part('templates/parts/price', 'woocommerce', '', $params);

        ?>

        <?php
        cyberstore_mikado_get_module_template_part('templates/parts/add-to-cart', 'woocommerce', '', $params);

        ?>
    </div>
<?php endwhile;
else: ?>
    <li class="mkd-product-pvd-messsage">
        <?php cyberstore_mikado_get_module_template_part('templates/parts/no-posts', 'woocommerce', '', $params); ?>
    </li>
<?php endif;
wp_reset_postdata();
?>