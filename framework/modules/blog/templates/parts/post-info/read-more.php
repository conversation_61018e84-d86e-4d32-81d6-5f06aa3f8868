<?php if ( ! cyberstore_mikado_post_has_read_more() ) { ?>
	<div class="mkd-post-read-more-button">
		<?php
		if ( cyberstore_mikado_core_plugin_installed() ) {
			echo cyberstore_mikado_get_button_html(
				apply_filters(
					'cyberstore_mikado_blog_template_read_more_button',
					array(
						'type'         => 'simple',
						'size'         => 'medium',
						'link'         => get_the_permalink(),
						'text'         => esc_html__( 'READ MORE', 'cyberstore' ),
						'custom_class' => 'mkd-blog-list-button'
					)
				)
			);
		} else { ?>
			<a itemprop="url" href="<?php echo esc_url( get_the_permalink() ); ?>" target="_self" class="mkd-btn mkd-btn-medium mkd-btn-simple mkd-blog-list-button">
                <span class="mkd-btn-text"><?php echo esc_html__( 'READ MORE', 'cyberstore' ); ?></span>
			</a>
		<?php } ?>
	</div>
<?php } ?>