<div
    class="mkd-pl-holder <?php echo esc_attr($holder_classes) ?>" <?php echo wp_kses($holder_data, array('data')); ?>>

    <?php
    if ($query_result->have_posts()):

        if ($show_category_filter == 'yes' || $show_ordering_filter == 'yes'):

            ?>

            <div class="mkd-pl-filters clearfix">

                <?php
                echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/categories-filter', 'product-list-advanced', '', $params);
                echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/ordering-filter', 'product-list-advanced', '', $params);
                ?>

            </div>

        <?php endif; ?>

        

        <div class="mkd-pl-outer mkd-outer-space clearfix">

            <?php
            while ($query_result->have_posts()) : $query_result->the_post();
                echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/advanced-list-left', 'product-list-advanced', '', $params);
            endwhile;
            ?>
        </div>
    <?php
    else:
        cyberstore_mikado_get_module_template_part('templates/parts/no-posts', 'woocommerce', '', $params);

        ?>

    <?php
    endif;
    wp_reset_postdata();
    ?>
</div>