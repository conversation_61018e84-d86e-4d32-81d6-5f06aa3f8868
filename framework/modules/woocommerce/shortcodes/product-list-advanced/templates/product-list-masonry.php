<div
    class="mkd-pl-holder mkd-pl-masonry-gallery-layout <?php echo esc_attr($holder_classes) ?>" <?php echo wp_kses($holder_data, array('data')); ?>>

    <?php
    if ($query_result->have_posts()):

        if ($show_category_filter == 'yes' || $show_ordering_filter == 'yes') {
            ?>

            <div class="mkd-pl-filters clearfix">

                <?php
                echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/categories-filter', 'product-list-advanced', '', $params);
                echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/ordering-filter', 'product-list-advanced', '', $params);
                ?>

            </div>

        <?php } ?>

        <div class="mkd-pl-outer mkd-outer-space">
            <div class="mkd-pl-sizer"></div>
            <div class="mkd-pl-gutter"></div>

            <?php if ($show_category_filter == 'yes' || $show_ordering_filter == 'yes') {
                echo cyberstore_mikado_loading_spinner_rotate_circles();
            } ?>

            <?php
            while ($query_result->have_posts()) : $query_result->the_post();
                $advanced_size_meta = get_post_meta(get_the_ID(), 'mkd_product_advanced_list_size', true);
                $advanced_size_meta = !empty($advanced_size_meta) ? $advanced_size_meta : 'left';

                echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/advanced-list-' . $advanced_size_meta, 'product-list-advanced', '', $params);
            endwhile; ?>

        </div>

    <?php else:
        cyberstore_mikado_get_module_template_part('templates/parts/no-posts', 'woocommerce', '', $params);
    endif;
    wp_reset_postdata();
    ?>
</div>