<?php
$product = cyberstore_mikado_return_woocommerce_global_variable();

$classes = array();
$classes[] = 'mkd-' . esc_attr($class_name) . '-price';

$value_deal = '';
$value_deal_label = '';
$sale_price_to = get_post_meta(get_the_ID(), '_sale_price_dates_to', true);
if (empty($sale_price_to)) {
	$sale_price_to=0;
}
$difference = $sale_price_to - time();


if ($difference > 0) {
    $classes[] = 'mkd-value-deal';

    $value_deal_label = esc_html__('Value Deal', 'cyberstore');
}

if ($display_price === 'yes' && $price_html = $product->get_price_html()) { ?>

    <div <?php cyberstore_mikado_class_attribute($classes) ?>>

        <?php echo wp_kses_post($price_html);
        if (!empty($value_deal_label)) { ?>

            <span class="mkd-value-deal-label">(<?php echo esc_attr($value_deal_label) ?>)</span>

        <?php } ?>

    </div>

<?php } ?>