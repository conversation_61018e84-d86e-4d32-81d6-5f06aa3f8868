<?php

$sale_price_to = get_post_meta(get_the_ID(), '_sale_price_dates_to', true);

if (!empty($sale_price_to)) {

    $sale_price_date_year = date("Y", $sale_price_to);
    $sale_price_date_month = date("n", $sale_price_to);
    $sale_price_date_day = date("j", $sale_price_to);


    if (!empty($sale_price_date_year) && !empty($sale_price_date_month) && !empty($sale_price_date_day)) {
        $id = mt_rand(1000, 9999);

        ?>

        <h5 class="mkd-offer"><?php esc_html_e('Hurry up! Offer ends in:', 'cyberstore'); ?></h5>

        <div class="mkd-vd-countdown-holder">
            <div class="mkd-vd-countdown-holder-inner">
                <div class="mkd-vd-countdown" id="countdown<?php echo esc_attr($id); ?>"
                     data-year="<?php echo esc_attr($sale_price_date_year); ?>"
                     data-month="<?php echo esc_attr($sale_price_date_month); ?>"
                     data-day="<?php echo esc_attr($sale_price_date_day); ?>"
                     data-hour="0"
                     data-minute="0"
                     data-timezone="<?php echo get_option('gmt_offset'); ?>">
                </div>
                <div class="mkd-vd-countdown-labels">
                    <h6 class="mkd-vd-countdown-hours"><?php esc_html_e('Hours', 'cyberstore'); ?></h6>
                    <h6 class="mkd-vd-countdown-mins"><?php esc_html_e('Mins', 'cyberstore'); ?></h6>
                    <h6 class="mkd-vd-countdown-secs"><?php esc_html_e('Secs', 'cyberstore'); ?></h6>
                </div>
            </div>
        </div>

    <?php }
} ?>