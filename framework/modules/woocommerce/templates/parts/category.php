<?php

if ($display_category === 'yes') {
	$product = cyberstore_mikado_return_woocommerce_global_variable();
	$categories = wp_get_post_terms($product->get_id(), 'product_cat');

	if (!empty($categories) && !is_wp_error($categories)) {
		$first_category = $categories[0];
		$category_link  = get_term_link($first_category);
		$category_name  = esc_html($first_category->name);

		if (!is_wp_error($category_link)) { ?>
			<p class="mkd-<?php echo esc_attr($class_name); ?>-category">
				<a href="<?php echo esc_url($category_link); ?>"><?php echo $category_name; ?></a>
			</p>
		<?php }
	}
}
?>
