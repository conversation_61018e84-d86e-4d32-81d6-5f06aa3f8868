<?php
/*
Template Name: WooCommerce
*/
?>
<?php
// This template handles WooCommerce pages except single products
// Single products are handled by woocommerce/single-product.php

// If this is a single product page, don't use this template
if ( is_singular( 'product' ) ) {
	return;
}

$mkd_sidebar_layout = cyberstore_mikado_sidebar_layout();

get_header();
cyberstore_mikado_get_title();
get_template_part( 'slider' );
?>

<div class="mkd-container">
	<div class="mkd-container-inner clearfix">
		<div class="mkd-grid-row">
			<div <?php echo cyberstore_mikado_get_content_sidebar_class(); ?>>
				<?php cyberstore_mikado_woocommerce_content(); ?>
			</div>
			<?php if ( $mkd_sidebar_layout !== 'no-sidebar' ) { ?>
				<div <?php echo cyberstore_mikado_get_sidebar_holder_class(); ?>>
					<?php get_sidebar(); ?>
				</div>
			<?php } ?>
		</div>
	</div>
</div>

<?php get_footer(); ?>