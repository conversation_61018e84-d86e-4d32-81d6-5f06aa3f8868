<?php
/**
 * The Template for displaying all single products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://docs.woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     1.6.4
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

$mkd_sidebar_layout = cyberstore_mikado_sidebar_layout();

get_header();
// Removed cyberstore_mikado_get_title() to hide the "Shop" title
get_template_part( 'slider' );
?>

<style>
/* ========================================
   COMIC WAREHOUSE - SINGLE PRODUCT STYLES
   Custom styling for individual product pages
   ======================================== */

/* Main product container - centers content and sets max width */
.cw-single-product-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 60px 40px;
}

/* Override theme container padding for better spacing */
.mkd-container .cw-single-product-container {
	padding-top: 80px;
	padding-bottom: 80px;
	max-width: 1200px;
}

/* Breadcrumb Navigation Styling */
.cw-breadcrumb-container {
	margin-top: 90px;
}

.cw-breadcrumb-list {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	list-style: none;
	margin: 0;
	padding: 0;
	font-size: 14px;
	color: #666;
}

.cw-breadcrumb-item {
	display: flex;
	align-items: center;
}

.cw-breadcrumb-link {
	color: #666;
	text-decoration: none;
	transition: color 0.3s ease;
}

.cw-breadcrumb-link:hover {
	color: #007cba;
	text-decoration: underline;
}

.cw-breadcrumb-separator {
	margin: 0 8px;
	color: #999;
	font-size: 12px;
}

.cw-breadcrumb-current {
	color: #333;
	font-weight: 500;
	max-width: 300px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* ========================================
   SIMILAR PRODUCTS SECTION
   Grid layout for related product recommendations
   ======================================== */

/* Main container for similar products section */
.cw-similar-products-section {
	background-color: #f8f9fa;

}

/* Section title styling */
.cw-similar-products-title {
	text-align: center;
	font-size: 2rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 40px;
	font-family: inherit;
}

/* Grid layout for product cards - 4 products per row */
.cw-similar-products-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20px;
	max-width: 1200px;
	margin: 0 auto;
}

.cw-product-card {
	background: white;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 4px 12px rgba(0,0,0,0.1);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	position: relative;
	height: 100%;
}

.cw-product-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* ========================================
   MAIN PRODUCT INFORMATION STYLING
   Styles for product title, SKU, and key details
   ======================================== */

/* Main product title - large and prominent */
.cw-product-title-main {
	font-size: 1.8rem;
	font-weight: 700;
	color: #000;
	margin-bottom: 10px;
}

/* Product SKU (Stock Keeping Unit) display */
.cw-product-sku {
	margin-bottom: 15px;
	font-size: 14px;
	color: #666;
}

/* SKU label styling */
.cw-sku-label {
	font-weight: 600;
	color: #333;
	margin-right: 8px;
}

/* SKU value styling - technical appearance */
.cw-sku-value {
	font-family: 'Courier New', monospace;
	background-color: #f5f5f5;
	padding: 2px 6px;
	border-radius: 3px;
	font-size: 13px;
	color: #555;
}

.cw-product-image-wrapper {
	position: relative;
	overflow: hidden;
}

.cw-product-image-wrapper img {
	width: 100%;
	height: 250px;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.cw-product-card:hover .cw-product-image-wrapper img {
	transform: scale(1.05);
}

.cw-product-badges {
	position: absolute;
	top: 12px;
	left: 12px;
	z-index: 2;
}

.cw-badge {
	display: inline-block;
	padding: 4px 12px;
	border-radius: 20px;
	font-size: 0.75rem;
	font-weight: 600;
	text-transform: uppercase;
	margin-right: 8px;
	margin-bottom: 4px;
}

.cw-sale-badge {
	background-color: #6f42c1;
	color: white;
}

.cw-in-stock-badge {
	background-color: #28a745;
	color: white;
}

.cw-out-of-stock-badge {
	background-color: #dc3545;
	color: white;
}

.cw-wishlist-btn {
	position: absolute;
	top: 12px;
	right: 12px;
	background: rgba(255,255,255,0.9);
	border: none;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	z-index: 2;
}

.cw-wishlist-btn:hover {
	background: white;
	color: #e74c3c;
	transform: scale(1.1);
}

.cw-product-info {
	padding: 20px;
}

.cw-product-category {
	font-size: 0.85rem;
	color: #582C83;
	text-transform: uppercase;
	font-weight: 500;
	margin-bottom: 8px;
}


.cw-product-title {
	margin: 0 0 12px 0;
	font-size: 1.1rem;
	font-weight: 600;
	line-height: 1.3;
	height: 2.6em;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.cw-product-title a {
	color: #333;
	text-decoration: none;
	transition: color 0.3s ease;
	font-size: 18px;
}

.cw-product-title a:hover {
	color: #007cba;
}

.cw-product-price {
	font-size: 1.2rem;
	font-weight: 700;
	color: #333;
	margin-bottom: 20px;
}

.cw-product-price .woocommerce-Price-amount {
	color: #333;
}

.cw-product-actions {
	display: flex;
	gap: 10px;
}

.cw-add-to-cart-btn,
.cw-view-product-btn {
	flex: 1;
	border: none;
	height: 48px;
	font-weight: 600;
	text-decoration: none;
	text-align: center;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 16px;
	align-items: center;
	justify-content: center;
}

.cw-add-to-cart-btn {
	background-color: #333;
	color: white;
}

.cw-add-to-cart-btn:hover {
	background-color: #555;
	transform: translateY(-1px);
}

.cw-view-product-btn {
	background-color: transparent;
	color: #333;
	display: flex;
	align-items: center;
	border: 1px solid #ddd;
	
}

.cw-view-product-btn:hover {
	background-color: #f8f9fa;
	border-color: #333;
	transform: translateY(-1px);
}

.cw-product-layout {
	margin-top: 40px;
	display: flex;
	gap: 60px;
	align-items: flex-start;
}

.cw-product-images {
	flex: 1;
	max-width: 500px;
}

.cw-product-image-main {
	border-radius: 12px;
	padding: 20px;
	background: white;
	margin-bottom: 20px;
}

.cw-product-image-main img {
	width: 100%;
	height: auto;
	display: block;
}

.cw-product-details {
	flex: 1;
	max-width: 500px;
	
}

.cw-product-top {
	background-color:#F7F7F7;
	border-radius: 30px;
	padding: 40px;
}

.cw-product-badge {
	background: #8b5cf6;
	color: white;
	padding: 6px 16px;
	border-radius: 20px;
	font-size: 12px;
	font-weight: 600;
	text-transform: uppercase;
	display: inline-block;
	margin-bottom: 16px;
}

.cw-product-category {
	color: #582C83;
	font-size: 14px;
	margin-bottom: 8px;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.cw-product-title {
	font-size: 32px;
	font-weight: 700;
	color: #333;
	margin-bottom: 20px;
	line-height: .8;
}

.cw-product-description {
	color: #666;
	line-height: 1.6;
	margin-bottom: 30px;
}

.cw-product-options {
	margin-bottom: 30px;
}

.cw-product-option-label {
	font-weight: 600;
	color: #333;
	margin-bottom: 12px;
	display: block;
}

.cw-product-variants {
	display: flex;
	gap: 12px;
	margin-bottom: 20px;
}

.cw-variant-option {
	padding: 8px 16px;
	border: 2px solid #e0e0e0;
	border-radius: 100px;
	background: white;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cw-variant-option:hover,
.cw-variant-option.selected {
	background: #7F56D9;
	color:#fff;
}

/* Product Variations Container */
.cw-product-variations {
	margin-bottom: 30px;
}

/* Color Swatches Styling */
.cw-color-variants {
	display: flex;
	gap: 15px;
	flex-wrap: wrap;
	align-items: center;
}

.cw-color-option {
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: pointer;
	transition: all 0.3s ease;
	padding: 8px;
	border-radius: 12px;
}

.cw-color-option:hover {
	background: #f8f9fa;
}

.cw-color-option.selected {
	background: #f0f0ff;
}

.cw-color-swatch {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	border: 3px solid #e0e0e0;
	margin-bottom: 8px;
	transition: all 0.3s ease;
	position: relative;
}

.cw-color-option.selected .cw-color-swatch {
	border-color: #7F56D9;
	box-shadow: 0 0 0 2px rgba(127, 86, 217, 0.2);
}

.cw-color-name {
	font-size: 12px;
	color: #666;
	text-align: center;
	font-weight: 500;
}

.cw-color-option.selected .cw-color-name {
	color: #7F56D9;
	font-weight: 600;
}

/* Size Options Styling */
.cw-size-variants {
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

.cw-size-option {
	width: 50px;
	height: 50px;
	border: 2px solid #e0e0e0;
	border-radius: 50%;
	background: white;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 14px;
	font-weight: 600;
	color: #666;
	display: flex;
	align-items: center;
	justify-content: center;
}

.cw-size-option:hover {
	border-color: #7F56D9;
	background: #f8f6ff;
	color: #7F56D9;
}

.cw-size-option.selected {
	border-color: #7F56D9;
	background: #7F56D9;
	color: white;
}



.cw-quantity-section {
	margin-bottom: 30px;
}

.cw-quantity-label {
	font-weight: 600;
	color: #333;
	margin-bottom: 12px;
	display: block;
	font-size: 18px;
}



/* Modern Quantity Selector Container - Separated Design */
.mkd-quantity-buttons,
.mkd-quantity-buttons.quantity {
	display: flex !important;
	flex-direction: row !important;
	align-items: center !important;
	justify-content: flex-start !important;
	gap: 12px !important;
	max-width: 300px !important;
	margin-top: 15px !important;
	background: transparent !important;
	border: none !important;
	flex-wrap: nowrap !important;
}

/* Hide the default quantity label inside the container */
.mkd-quantity-buttons .mkd-quantity-label {
	display: none;
}

/* Quantity decrease button - Separated design */
.mkd-quantity-minus,
.mkd-quantity-minus.dripicons-chevron-left {
	width: 60px !important;
	height: 50px !important;
	border: 2px solid #e0e0e0 !important;
	border-radius: 12px !important;
	background: white !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	cursor: pointer !important;
	transition: all 0.3s ease !important;
	font-size: 0 !important;
	color: #666 !important;
	margin: 0 !important;
	padding: 0 !important;
}

.mkd-quantity-minus:hover,
.mkd-quantity-minus.dripicons-chevron-left:hover {
	background: #f8f9fa !important;
	border-color: #ccc !important;
}

.mkd-quantity-minus:before,
.mkd-quantity-minus.dripicons-chevron-left:before {
	content: '−' !important;
	font-size: 24px !important;
	line-height: 1 !important;
	font-family: inherit !important;
}

/* Quantity input field - Separated design */
.mkd-quantity-input,
.mkd-quantity-buttons .mkd-quantity-input {
	width: 80px !important;
	height: 50px !important;
	border: 2px solid #e0e0e0 !important;
	border-radius: 12px !important;
	background: white !important;
	text-align: center !important;
	font-size: 18px !important;
	font-weight: 600 !important;
	color: #333 !important;
	outline: none !important;
	margin: 0 !important;
	padding: 0 !important;
	transition: all 0.3s ease !important;
}

.mkd-quantity-input:focus,
.mkd-quantity-buttons .mkd-quantity-input:focus {
	border-color: #7F56D9 !important;
	box-shadow: 0 0 0 3px rgba(127, 86, 217, 0.1) !important;
	outline: none !important;
}

/* Quantity increase button - Separated design */
.mkd-quantity-plus,
.mkd-quantity-plus.dripicons-chevron-right {
	width: 60px !important;
	height: 50px !important;
	border: 2px solid #e0e0e0 !important;
	border-radius: 12px !important;
	background: white !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	cursor: pointer !important;
	transition: all 0.3s ease !important;
	font-size: 0 !important;
	color: #666 !important;
	margin: 0 !important;
	padding: 0 !important;
}

.mkd-quantity-plus:hover,
.mkd-quantity-plus.dripicons-chevron-right:hover {
	background: #f8f9fa !important;
	border-color: #ccc !important;
}

.mkd-quantity-plus:before,
.mkd-quantity-plus.dripicons-chevron-right:before {
	content: '+' !important;
	font-size: 24px !important;
	line-height: 1 !important;
	font-family: inherit !important;
}

/* Legacy quantity input styling (fallback) */
.cw-quantity-input {
	width: 80px;
	padding: 12px;
	border: 2px solid #e0e0e0;
	border-radius: 6px;
	text-align: center;
	font-size: 16px;
}

.cw-price-section {
	margin-bottom: 30px;
}

.cw-price {
	font-size: 28px;
	font-weight: 700;
	color: #333;
	margin-right: 15px;
}

.cw-stock-status {
	color: #28a745;
	font-size: 14px;
	font-weight: 600;
}

.cw-add-to-cart-btn {
	width: 100% !important;
	background-color: #2D2926 !important;
	color: white;
	padding: 12px 24px;
	border: none;
	border-radius: 100px;
	font-size: 16px;
	font-weight: 600;
	cursor: pointer;
	transition: background 0.3s ease;
	margin-bottom: 30px;
	font-family: 'Figtree', sans-serif;
	font-weight: 400;
	height: 48px;
}

.cw-add-to-cart-btn:hover {
	background: #555;
}

.cw-add-to-cart-btn , .cw-similar-add-to-cart {
	font-size: 12px;
}

.cw-view-product-btn {
	border-radius: 100px;
	font-size: 12px;
}

/* ========================================
   PRODUCT INFORMATION TABS
   Interactive tabs for Description, Additional Info, Reviews
   ======================================== */

/* Main tabs section container */
.cw-product-tabs-section {
	margin-top: 60px;
	background-color: #f8f9fa;
	padding: 40px 0;
}

/* Inner container for tabs content */
.cw-tabs-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

/* Tab navigation buttons container */
.cw-tab-navigation {
	display: flex;
	gap: 8px;
	margin-bottom: 30px;
}

/* Individual tab button styling */
.cw-tab-btn {
	padding: 12px 24px;
	background: #e9ecef;
	border: none;
	font-size: 14px;
	font-weight: 500;
	color: #6c757d;
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 25px;
	position: relative;
}

/* Tab button hover state */
.cw-tab-btn:hover {
	background-color: #dee2e6;
	color: #495057;
}

/* Active tab button styling */
.cw-tab-btn.active {
	background-color: #6f42c1;
	color: white;
	font-weight: 600;
}

.cw-tab-content-wrapper {
	background: white;
	border-radius: 8px;
	padding: 30px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.cw-tab-content {
	display: none;
	animation: fadeIn 0.3s ease-in-out;
}

.cw-tab-content.active {
	display: block;
}

@keyframes fadeIn {
	from { opacity: 0; transform: translateY(10px); }
	to { opacity: 1; transform: translateY(0); }
}

.cw-tab-title {
	font-size: 20px;
	font-weight: 700;
	color: #212529;
	margin-bottom: 20px;
	border-bottom: 2px solid #f8f9fa;
	padding-bottom: 10px;
}

.cw-tab-text {
	color: #6c757d;
	line-height: 1.7;
	font-size: 15px;
}

.cw-tab-text p {
	margin-bottom: 16px;
	color: #495057;
}

/* Additional Information Table Styling */
.cw-product-attributes {
	width: 100%;
	border-collapse: collapse;
	margin-top: 20px;
}

.cw-product-attributes tr {
	border-bottom: 1px solid #f0f0f0;
}

.cw-attribute-label {
	padding: 12px 0;
	font-weight: 600;
	color: #333;
	width: 30%;
	vertical-align: top;
}

.cw-attribute-value {
	padding: 12px 0;
	color: #666;
	vertical-align: top;
}

/* ========================================
   NEWSLETTER SIGNUP SECTION
   Dark themed newsletter with comic character background
   ======================================== */

/* Main newsletter section container */
.cw-newsletter-section {
	margin-top: 80px;
	padding: 0 20px;
	margin-bottom: 80px;
}

/* Newsletter content container with dark background and rounded corners */
.cw-newsletter-container {
	max-width: 1200px;
	margin: 0 auto;
	background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
	border-radius: 20px;
	overflow: hidden;
	position: relative;
}

/* Full-width background image overlay */
.cw-newsletter-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image: var(--bg-image);
	background-size: cover;
	background-position: center right;
	background-repeat: no-repeat;
	opacity: 0.9;
	z-index: 1;
}

/* Default placeholder background when no image is uploaded */
.cw-newsletter-container:not([style*="--bg-image"])::before {
	background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 300"><defs><radialGradient id="mask" cx="80%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><rect width="1200" height="300" fill="url(%23mask)"/><circle cx="960" cy="120" r="8" fill="%2300ff88" opacity="0.8"/><circle cx="1020" cy="140" r="6" fill="%2300ff88" opacity="0.6"/></svg>');
}

/* Inner content wrapper - now just contains text content */
.cw-newsletter-content {
	position: relative;
	z-index: 2;
	min-height: 300px;
	display: flex;
	align-items: center;
}

/* Text and form content - now takes full width */
.cw-newsletter-text {
	width: 100%;
	max-width: 600px;
	padding: 60px 50px;
	color: white;
	position: relative;
}

/* Newsletter main title */
.cw-newsletter-title {
	font-size: 2.5rem;
	font-weight: 700;
	color: white;
	margin-bottom: 20px;
	line-height: 1.2;
}

/* Newsletter description text */
.cw-newsletter-description {
	font-size: 1rem;
	line-height: 1.6;
	color: rgba(255, 255, 255, 0.9);
	margin-bottom: 30px;
}

/* Form container */
.cw-newsletter-form {
	width: 100%;
}

/* Form input group - contains email input and button */
.cw-form-group {
	display: flex;
	gap: 0;
	margin-bottom: 15px;
	max-width: 400px;
}

/* Email input field */
.cw-email-input {
	flex: 1;
	padding: 15px 20px;
	border: none;
	border-radius:25px !important;
	font-size: 16px;
	background: white;
	color: #333;
	outline: none;
	margin-right: 10px !important;
	background-color: #F7F7F7 !important;
}

/* Email input placeholder styling */
.cw-email-input::placeholder {
	color: #999;
}

/* Sign up button */
.cw-signup-btn {
	padding: 0 20px !important;
	background: #FFFFFF !important;
	color: white;
	border: none;
	border-radius: 25px;
	font-size: 16px;
	font-weight: 600;
	cursor: pointer;
	transition: background 0.3s ease;
	white-space: nowrap;
	height: 48px;
	color:#2D2926 !important;
}

/* Sign up button hover effect */
.cw-signup-btn:hover {
	background: #5a2d91;
	transform: translateY(-1px);
}

/* Terms and conditions text */
.cw-terms-text {
	font-size: 0.85rem;
	color: rgba(255, 255, 255, 0.7);
	margin: 0;
	line-height: 1.4;
}



/* ========================================
   RESPONSIVE STYLES FOR NEWSLETTER
   ======================================== */

/* Tablet styles */
@media (max-width: 768px) {
	.cw-newsletter-content {
		flex-direction: column;
		text-align: center;
	}

	.cw-newsletter-text {
		padding: 40px 30px 20px;
	}

	.cw-newsletter-title {
		font-size: 2rem;
	}

	.cw-form-group {
		max-width: 350px;
		margin: 0 auto 15px;
	}
}

/* Mobile styles */
@media (max-width: 480px) {
	.cw-newsletter-section {
		margin-top: 60px;
		padding: 0 15px;
	}

	.cw-newsletter-container {
		border-radius: 15px;
	}

	.cw-newsletter-text {
		padding: 30px 25px 20px;
	}

	.cw-newsletter-title {
		font-size: 1.75rem;
		margin-bottom: 15px;
	}

	.cw-newsletter-description {
		font-size: 0.9rem;
		margin-bottom: 25px;
	}

	.cw-form-group {
		flex-direction: column;
		max-width: 280px;
		gap: 10px;
	}

	.cw-email-input {
		border-radius: 25px;
		text-align: center;
		background-color: #F7F7F7 !important;
	}

	.cw-signup-btn {
		border-radius: 25px;
	}

	.cw-terms-text {
		font-size: 0.8rem;
		padding: 0 10px;
	}
}


.cw-product-tabs {
	margin-top: 40px;
}

.cw-tab-item {
	margin-bottom: 20px;
}

.cw-tab-header {
	padding: 32px 48px;
	font-weight: 600;
	color: #333;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #F7F7F7 !important;
	border-radius: 30px;
}

.cw-tab-header:hover {
	color: #007cba;
}

.cw-tab-content {
	padding: 10px 32px;
	color: #666;
	line-height: 1.6;
	display: none;
}

.cw-tab-content.active {
	display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
	.cw-product-layout {
		flex-direction: column;
		gap: 30px;
	}

	.cw-product-images,
	.cw-product-details {
		max-width: 100%;
	}

	.cw-product-title {
		font-size: 24px;
	}

	.cw-single-product-container {
		padding: 40px 20px;
	}

	.mkd-container .cw-single-product-container {
		padding-top: 60px;
		padding-bottom: 60px;
	}

	.cw-breadcrumb-container {
		margin-bottom: 20px;
	}

	.cw-breadcrumb-list {
		font-size: 13px;
	}

	.cw-breadcrumb-current {
		max-width: 200px;
	}
}

/* Tablet responsive */
@media (max-width: 768px) {
	.cw-similar-products-grid {
		grid-template-columns: repeat(3, 1fr);
		gap: 18px;
	}

	.cw-product-image-wrapper img {
		height: 220px;
	}
}

@media (max-width: 480px) {
	.cw-single-product-container {
		padding: 30px 15px;
	}

	.mkd-container .cw-single-product-container {
		padding-top: 40px;
		padding-bottom: 40px;
	}

	.cw-similar-products-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 15px;
	}

	.cw-product-card {
		max-width: none;
	}

	.cw-product-image-wrapper img {
		height: 200px;
	}

	.cw-product-info {
		padding: 15px;
	}

	.cw-product-actions {
		flex-direction: column;
		gap: 8px;
	}

	/* Mobile quantity selector adjustments */
	.mkd-quantity-buttons.quantity {
		max-width: 250px;
		gap: 8px;
	}

	.mkd-quantity-minus,
	.mkd-quantity-plus,
	.mkd-quantity-minus.dripicons-chevron-left,
	.mkd-quantity-plus.dripicons-chevron-right {
		width: 50px;
		height: 45px;
		border-radius: 10px;
	}

	.mkd-quantity-input,
	.mkd-quantity-buttons .mkd-quantity-input {
		width: 70px;
		height: 45px;
		font-size: 16px;
		border-radius: 10px;
	}
}

</style>


<!-- Main theme container wrapper -->
<div class="mkd-container">
	<div class="mkd-container-inner clearfix">
		<!-- Custom single product container with max-width and centering -->
		<div class="cw-single-product-container">
			<?php while ( have_posts() ) : the_post(); ?>
				<?php
				// Get the global WooCommerce product object
				global $product;
				?>

				<!-- Breadcrumb Navigation -->
				<div class="cw-breadcrumb-container">
					<nav class="cw-breadcrumb" aria-label="Breadcrumb">
						<ol class="cw-breadcrumb-list">
							<li class="cw-breadcrumb-item">
								<a href="<?php echo esc_url(home_url('/')); ?>" class="cw-breadcrumb-link">Home</a>
							</li>
							<li class="cw-breadcrumb-separator">></li>
							<li class="cw-breadcrumb-item">
								<a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="cw-breadcrumb-link">Shop</a>
							</li>
							<?php
							// Get product categories
							$product_cats = wp_get_post_terms(get_the_ID(), 'product_cat');
							if (!empty($product_cats) && !is_wp_error($product_cats)) {
								// Use the first category for breadcrumb
								$main_cat = $product_cats[0];
								?>
								<li class="cw-breadcrumb-separator">></li>
								<li class="cw-breadcrumb-item">
									<a href="<?php echo esc_url(get_term_link($main_cat)); ?>" class="cw-breadcrumb-link">
										<?php echo esc_html($main_cat->name); ?>
									</a>
								</li>
								<?php
							}
							?>
							<li class="cw-breadcrumb-separator">></li>
							<li class="cw-breadcrumb-item cw-breadcrumb-current" aria-current="page">
								<?php echo esc_html(get_the_title()); ?>
							</li>
						</ol>
					</nav>
				</div>

				<!-- Main product layout: side-by-side images and details -->
				<div class="cw-product-layout">
					<!-- Left side: Product Images -->
					<div class="cw-product-images">
						<div class="cw-product-image-main">
							<?php
							// Display featured image or placeholder if no image exists
							if ( has_post_thumbnail() ) {
								the_post_thumbnail( 'large' );
							} else {
								echo '<img src="' . wc_placeholder_img_src() . '" alt="' . esc_attr( get_the_title() ) . '">';
							}
							?>
						</div>
					</div>

					<!-- Right side: Product Details and Information -->
					<div class="cw-product-details">
						<div class="cw-product-top">
							<!-- Show "Special Edition" badge for featured products -->
							<?php if ( $product->is_featured() ) : ?>
								<span class="cw-product-badge">Special Edition</span>
							<?php endif; ?>

							<!-- Display product category -->
							<div class="cw-product-category">
								<?php
								// Get the first product category and display it
								$terms = get_the_terms( get_the_ID(), 'product_cat' );
								if ( $terms && ! is_wp_error( $terms ) ) {
									echo esc_html( $terms[0]->name );
								}
								?>
							</div>

							<!-- Main product title -->
							<h1 class="cw-product-title-main"><?php the_title(); ?></h1>

							<!-- Product SKU Display - only shows if SKU exists -->
							<?php if ( $product->get_sku() ) : ?>
								<div class="cw-product-sku">
									<span class="cw-sku-label">SKU:</span>
									<span class="cw-sku-value"><?php echo esc_html( $product->get_sku() ); ?></span>
								</div>
							<?php endif; ?>

							<!-- Product short description/excerpt -->
							<div class="cw-product-description">
								<?php the_excerpt(); ?>
							</div>

							<?php if ( $product->is_type( 'variable' ) ) : ?>
								<div class="cw-product-variations">
									<?php
									$attributes = $product->get_variation_attributes();
									$available_variations = $product->get_available_variations();

									foreach ( $attributes as $attribute_name => $options ) :
										$attribute_label = wc_attribute_label( $attribute_name );
										$attribute_slug = str_replace( 'pa_', '', $attribute_name );
										?>
										<div class="cw-product-options">
											<label class="cw-product-option-label"><?php echo esc_html( $attribute_label ); ?></label>

											<?php if ( $attribute_slug === 'color' || strpos( $attribute_slug, 'color' ) !== false ) : ?>
												<!-- Color Swatches -->
												<div class="cw-color-variants">
													<?php foreach ( $options as $option ) :
														$term = get_term_by( 'slug', $option, $attribute_name );
														$color_value = get_term_meta( $term->term_id, 'color', true );
														$color_name = $term ? $term->name : $option;
														?>
														<div class="cw-color-option" data-value="<?php echo esc_attr( $option ); ?>" title="<?php echo esc_attr( $color_name ); ?>">
															<div class="cw-color-swatch" style="background-color: <?php echo esc_attr( $color_value ? $color_value : '#' . $option ); ?>"></div>
															<span class="cw-color-name"><?php echo esc_html( $color_name ); ?></span>
														</div>
													<?php endforeach; ?>
												</div>

											<?php elseif ( $attribute_slug === 'size' || strpos( $attribute_slug, 'size' ) !== false ) : ?>
												<!-- Size Options -->
												<div class="cw-size-variants">
													<?php foreach ( $options as $option ) :
														$term = get_term_by( 'slug', $option, $attribute_name );
														$size_name = $term ? $term->name : $option;
														?>
														<div class="cw-size-option" data-value="<?php echo esc_attr( $option ); ?>">
															<?php echo esc_html( strtoupper( $size_name ) ); ?>
														</div>
													<?php endforeach; ?>
												</div>

											<?php else : ?>
												<!-- Default Variant Options -->
												<div class="cw-product-variants">
													<?php foreach ( $options as $option ) :
														$term = get_term_by( 'slug', $option, $attribute_name );
														$option_name = $term ? $term->name : $option;
														?>
														<div class="cw-variant-option" data-value="<?php echo esc_attr( $option ); ?>">
															<?php echo esc_html( $option_name ); ?>
														</div>
													<?php endforeach; ?>
												</div>
											<?php endif; ?>

											<!-- Hidden select for WooCommerce -->
											<select name="attribute_<?php echo esc_attr( sanitize_title( $attribute_name ) ); ?>" class="cw-hidden-select" style="display: none;">
												<option value="">Choose <?php echo esc_html( $attribute_label ); ?></option>
												<?php foreach ( $options as $option ) : ?>
													<option value="<?php echo esc_attr( $option ); ?>"><?php echo esc_html( $option ); ?></option>
												<?php endforeach; ?>
											</select>
										</div>
									<?php endforeach; ?>
								</div>
							<?php endif; ?>

							<form class="cart" action="<?php echo esc_url( apply_filters( 'woocommerce_add_to_cart_form_action', $product->get_permalink() ) ); ?>" method="post" enctype='multipart/form-data'>
								<div class="cw-quantity-section">
									<label class="cw-quantity-label">Quantity</label>
									<?php
									woocommerce_quantity_input( array(
										'min_value'   => apply_filters( 'woocommerce_quantity_input_min', $product->get_min_purchase_quantity(), $product ),
										'max_value'   => apply_filters( 'woocommerce_quantity_input_max', $product->get_max_purchase_quantity(), $product ),
										'input_value' => isset( $_POST['quantity'] ) ? wc_stock_amount( wp_unslash( $_POST['quantity'] ) ) : $product->get_min_purchase_quantity(),
										'classes'     => array( 'cw-quantity-input' ),
									) );
									?>
								</div>

								<div class="cw-price-section">
									<span class="cw-price"><?php echo $product->get_price_html(); ?></span>
									<?php if ( $product->is_in_stock() ) : ?>
										<span class="cw-stock-status">✓ In Stock - Quick Delivery Available</span>
									<?php endif; ?>
								</div>

								<button type="submit" name="add-to-cart" value="<?php echo esc_attr( $product->get_id() ); ?>" class="cw-add-to-cart-btn">
									Add To Cart
								</button>

								<?php do_action( 'woocommerce_after_add_to_cart_button' ); ?>
							</form>
						</div>

						<div class="cw-product-tabs">
							<div class="cw-tab-item">
								<div class="cw-tab-header" onclick="toggleTab('description')">
									Product Description
									<span>+</span>
								</div>
								<div class="cw-tab-content" id="description">
									<?php the_content(); ?>
								</div>
							</div>

							<div class="cw-tab-item">
								<div class="cw-tab-header" onclick="toggleTab('additional')">
									Additional Information
									<span>+</span>
								</div>
								<div class="cw-tab-content" id="additional">
									<?php do_action( 'woocommerce_product_additional_information', $product ); ?>
								</div>
							</div>

							<div class="cw-tab-item">
								<div class="cw-tab-header" onclick="toggleTab('reviews')">
									Reviews
									<span>+</span>
								</div>
								<div class="cw-tab-content" id="reviews">
									<?php comments_template(); ?>
								</div>
							</div>
						</div>
					</div>
				</div>

			<?php endwhile; ?>
		</div>
	</div>
</div>

<!-- Product Information Tabs Section -->
<div class="cw-product-tabs-section">
	<div class="cw-tabs-container">
		<!-- Tab Navigation -->
		<div class="cw-tab-navigation">
			<button class="cw-tab-btn active" data-tab="description">Description</button>
			<button class="cw-tab-btn" data-tab="additional">Additional Information</button>
			<button class="cw-tab-btn" data-tab="reviews">Reviews</button>
		</div>

		<!-- Tab Content -->
		<div class="cw-tab-content-wrapper">
			<!-- Description Tab -->
			<div class="cw-tab-content active" id="description-tab">
				<h3 class="cw-tab-title">Description</h3>
				<div class="cw-tab-text">
					<?php
					// Get the product description
					global $product;
					$description = $product->get_description();
					if ( $description ) {
						echo wp_kses_post( $description );
					} else {
						the_content();
					}
					?>
				</div>
			</div>

			<!-- Additional Information Tab -->
			<div class="cw-tab-content" id="additional-tab">
				<h3 class="cw-tab-title">Additional Information</h3>
				<div class="cw-tab-text">
					<?php
					// Display product attributes/additional information
					$attributes = $product->get_attributes();
					if ( ! empty( $attributes ) ) {
						echo '<table class="cw-product-attributes">';
						foreach ( $attributes as $attribute ) {
							$name = $attribute->get_name();
							$options = $attribute->get_options();
							$values = array();

							if ( $attribute->is_taxonomy() ) {
								$terms = get_terms( array( 'taxonomy' => $name, 'include' => $options ) );
								foreach ( $terms as $term ) {
									$values[] = $term->name;
								}
							} else {
								$values = $options;
							}

							echo '<tr>';
							echo '<td class="cw-attribute-label">' . wc_attribute_label( $name ) . '</td>';
							echo '<td class="cw-attribute-value">' . implode( ', ', $values ) . '</td>';
							echo '</tr>';
						}
						echo '</table>';
					} else {
						echo '<p>No additional information available.</p>';
					}
					?>
				</div>
			</div>

			<!-- Reviews Tab -->
			<div class="cw-tab-content" id="reviews-tab">
				<h3 class="cw-tab-title">Reviews</h3>
				<div class="cw-tab-text">
					<?php
					// Display WooCommerce reviews
					if ( comments_open() || get_comments_number() ) {
						comments_template();
					} else {
						echo '<p>No reviews yet. Be the first to review this product!</p>';
					}
					?>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- ========================================
     SIMILAR PRODUCTS SECTION
     Displays 4 related products in a grid layout
     ======================================== -->
<div class="cw-similar-products-section">
	<div class="mkd-container">
		<div class="mkd-container-inner">
			<!-- Section heading -->
			<h2 class="cw-similar-products-title">Similar Products</h2>

			<!-- Grid container for product cards -->
			<div class="cw-similar-products-grid">
				<?php
				// Get related products based on product categories
				global $product;
				$related_limit = 4; // Show maximum 4 related products
				$related_ids = wc_get_related_products( $product->get_id(), $related_limit );

				// Check if we have related products to display
				if ( $related_ids ) {
					// Loop through each related product
					foreach ( $related_ids as $related_id ) {
						$related_product = wc_get_product( $related_id );
						// Make sure the product object is valid
						if ( $related_product ) {
							?>
							<!-- Individual product card -->
							<div class="cw-similar-product-item">
								<div class="cw-product-card">
									<!-- Product image with overlay elements -->
									<div class="cw-product-image-wrapper">
										<!-- Clickable product image -->
										<a href="<?php echo get_permalink( $related_id ); ?>">
											<?php echo $related_product->get_image( 'medium' ); ?>
										</a>

										<!-- Product status badges -->
										<div class="cw-product-badges">
											<?php if ( $related_product->is_on_sale() ) : ?>
												<span class="cw-badge cw-sale-badge">Special Edition</span>
											<?php endif; ?>
											<?php if ( !$related_product->is_in_stock() ) : ?>
												<span class="cw-badge cw-out-of-stock-badge">Out of Stock</span>
											<?php else : ?>
												<span class="cw-badge cw-in-stock-badge">In Stock</span>
											<?php endif; ?>
										</div>

										<!-- Wishlist button (top right corner) -->
										<button class="cw-wishlist-btn" title="Add to Wishlist">
											<i class="fa fa-heart-o"></i>
										</button>
									</div>
									<div class="cw-product-info">
										<div class="cw-product-category">
											<?php
											$categories = get_the_terms( $related_id, 'product_cat' );
											if ( $categories && !is_wp_error( $categories ) ) {
												echo esc_html( $categories[0]->name );
											}
											?>
										</div>
										<h3 class="cw-product-title">
											<a href="<?php echo get_permalink( $related_id ); ?>">
												<?php echo $related_product->get_name(); ?>
											</a>
										</h3>
										<div class="cw-product-price">
											<?php echo $related_product->get_price_html(); ?>
										</div>
										<div class="cw-product-actions">
											<button class="cw-add-to-cart-btn cw-similar-add-to-cart" data-product-id="<?php echo $related_id; ?>">
												Add to Cart
											</button>
											<a href="<?php echo get_permalink( $related_id ); ?>" class="cw-view-product-btn">
												View Product
											</a>
										</div>
									</div>
								</div>
							</div>
							<?php
						}
					}
				}
				?>
			</div>
		</div>
	</div>
</div>

<?php if ( cw_is_newsletter_enabled() ) : ?>
<!-- ========================================
     NEWSLETTER SIGNUP SECTION
     Dark themed newsletter signup with comic character background
     ======================================== -->
<div class="cw-newsletter-section">
	<div class="cw-newsletter-container" <?php if ( cw_get_newsletter_bg_image() ) : ?>style="--bg-image: url('<?php echo esc_url( cw_get_newsletter_bg_image() ); ?>');"<?php endif; ?>>
		<div class="cw-newsletter-content">
			<!-- Newsletter text and form -->
			<div class="cw-newsletter-text">
				<h2 class="cw-newsletter-title"><?php echo esc_html( cw_get_newsletter_title() ); ?></h2>
				<p class="cw-newsletter-description">
					<?php echo wp_kses_post( cw_get_newsletter_description() ); ?>
				</p>

				<!-- Newsletter signup form -->
				<form class="cw-newsletter-form" action="#" method="post">
					<div class="cw-form-group">
						<input type="email"
							   class="cw-email-input"
							   placeholder="Enter your email"
							   required
							   name="newsletter_email">
						<button type="submit" class="cw-signup-btn">Sign Up</button>
					</div>
					<p class="cw-terms-text">
						By clicking 'Sign Up' you're confirming that you agree with our Terms and Conditions.
					</p>
				</form>
			</div>
		</div>
	</div>
</div>
<?php endif; // End newsletter enabled check ?>

<script>
/* ========================================
   COMIC WAREHOUSE - SINGLE PRODUCT JAVASCRIPT
   Interactive functionality for product page
   ======================================== */

/**
 * Toggle accordion-style tabs (legacy function)
 * @param {string} tabId - The ID of the tab content to toggle
 */
function toggleTab(tabId) {
	const content = document.getElementById(tabId);
	const header = content.previousElementSibling;
	const icon = header.querySelector('span');

	// Check if tab is currently active
	if (content.classList.contains('active')) {
		// Close the active tab
		content.classList.remove('active');
		icon.textContent = '+';
	} else {
		// Close all other tabs first
		document.querySelectorAll('.cw-tab-content').forEach(tab => {
			tab.classList.remove('active');
		});
		document.querySelectorAll('.cw-tab-header span').forEach(span => {
			span.textContent = '+';
		});

		// Open the clicked tab
		content.classList.add('active');
		icon.textContent = '−';
	}
}

// Wait for DOM to be fully loaded before initializing functionality
document.addEventListener('DOMContentLoaded', function() {

	/* ========================================
	   SIMILAR PRODUCTS - ADD TO CART FUNCTIONALITY
	   Handles add to cart buttons in similar products section
	   ======================================== */
	const addToCartButtons = document.querySelectorAll('.cw-similar-add-to-cart');

	addToCartButtons.forEach(button => {
		button.addEventListener('click', function(e) {
			e.preventDefault(); // Prevent default form submission

			// Get product information
			const productId = this.getAttribute('data-product-id');
			const originalText = this.textContent;

			// Show loading state to user
			this.textContent = 'Adding...';
			this.disabled = true;

			// Simulate add to cart process (replace with actual AJAX call if needed)
			setTimeout(() => {
				// Show success state
				this.textContent = 'Added!';
				this.style.backgroundColor = '#28a745'; // Green success color

				// Reset button to original state after delay
				setTimeout(() => {
					this.textContent = originalText;
					this.style.backgroundColor = '';
					this.disabled = false;
				}, 1500);
			}, 500);
		});
	});

	/* ========================================
	   PRODUCT INFORMATION TABS FUNCTIONALITY
	   Handles switching between Description, Additional Info, Reviews
	   ======================================== */
	const tabButtons = document.querySelectorAll('.cw-tab-btn');
	const tabContents = document.querySelectorAll('.cw-tab-content');

	tabButtons.forEach(button => {
		button.addEventListener('click', function() {
			// Get the target tab from data attribute
			const targetTab = this.getAttribute('data-tab');

			// Remove active class from all tab buttons and content areas
			tabButtons.forEach(btn => btn.classList.remove('active'));
			tabContents.forEach(content => content.classList.remove('active'));

			// Add active class to clicked button and corresponding content
			this.classList.add('active');
			document.getElementById(targetTab + '-tab').classList.add('active');
		});
	});



	/* ========================================
	   PRODUCT VARIATIONS FUNCTIONALITY
	   Handles color swatches, size options, and other variations
	   ======================================== */
	const colorOptions = document.querySelectorAll('.cw-color-option');
	const sizeOptions = document.querySelectorAll('.cw-size-option');
	const variantOptions = document.querySelectorAll('.cw-variant-option');

	// Handle color selection
	colorOptions.forEach(option => {
		option.addEventListener('click', function() {
			// Remove selected class from all color options in the same group
			const parentGroup = this.closest('.cw-color-variants');
			if (parentGroup) {
				parentGroup.querySelectorAll('.cw-color-option').forEach(opt => opt.classList.remove('selected'));
			}
			// Add selected class to clicked option
			this.classList.add('selected');

			// Update hidden select
			const value = this.getAttribute('data-value');
			const hiddenSelect = this.closest('.cw-product-options').querySelector('.cw-hidden-select');
			if (hiddenSelect) {
				hiddenSelect.value = value;
				hiddenSelect.dispatchEvent(new Event('change'));
			}
		});
	});

	// Handle size selection
	sizeOptions.forEach(option => {
		option.addEventListener('click', function() {
			// Remove selected class from all size options in the same group
			const parentGroup = this.closest('.cw-size-variants');
			if (parentGroup) {
				parentGroup.querySelectorAll('.cw-size-option').forEach(opt => opt.classList.remove('selected'));
			}
			// Add selected class to clicked option
			this.classList.add('selected');

			// Update hidden select
			const value = this.getAttribute('data-value');
			const hiddenSelect = this.closest('.cw-product-options').querySelector('.cw-hidden-select');
			if (hiddenSelect) {
				hiddenSelect.value = value;
				hiddenSelect.dispatchEvent(new Event('change'));
			}
		});
	});

	// Handle variant selection
	variantOptions.forEach(option => {
		option.addEventListener('click', function() {
			// Remove selected class from all variant options in the same group
			const parentGroup = this.closest('.cw-product-variants');
			if (parentGroup) {
				parentGroup.querySelectorAll('.cw-variant-option').forEach(opt => opt.classList.remove('selected'));
			}
			// Add selected class to clicked option
			this.classList.add('selected');

			// Update hidden select
			const value = this.getAttribute('data-value');
			const hiddenSelect = this.closest('.cw-product-options').querySelector('.cw-hidden-select');
			if (hiddenSelect) {
				hiddenSelect.value = value;
				hiddenSelect.dispatchEvent(new Event('change'));
			}
		});
	});

	/* ========================================
	   QUANTITY SELECTOR FUNCTIONALITY
	   Handles +/- buttons for quantity input
	   ======================================== */
	const minusBtn = document.querySelector('.mkd-quantity-minus');
	const plusBtn = document.querySelector('.mkd-quantity-plus');
	const quantityInput = document.querySelector('.mkd-quantity-input');

	if (minusBtn && plusBtn && quantityInput) {
		minusBtn.addEventListener('click', function() {
			let currentValue = parseInt(quantityInput.value) || 1;
			let minValue = parseInt(quantityInput.getAttribute('data-min')) || 1;
			if (currentValue > minValue) {
				quantityInput.value = currentValue - 1;
			}
		});

		plusBtn.addEventListener('click', function() {
			let currentValue = parseInt(quantityInput.value) || 1;
			let maxValue = parseInt(quantityInput.getAttribute('data-max')) || 999;
			if (currentValue < maxValue) {
				quantityInput.value = currentValue + 1;
			}
		});
	}
});
</script>

<?php get_footer(); ?>
