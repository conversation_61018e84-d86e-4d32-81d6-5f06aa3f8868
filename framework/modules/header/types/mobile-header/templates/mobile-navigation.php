<?php do_action('cyberstore_mikado_before_mobile_navigation'); ?>

<?php if ( has_nav_menu( 'mobile-navigation' ) || has_nav_menu( 'main-navigation' ) ) { ?>
    <div class="mkd-mobile-side-area">
        <div class="mkd-close-mobile-side-area-holder">
            <?php echo cyberstore_mikado_icon_collections()->renderIcon('dripicons-cross', 'dripicons'); ?>
        </div>
        <div class="mkd-mobile-side-area-inner">
            <nav class="mkd-mobile-nav">
                <?php
                // Set main navigation menu as mobile if mobile navigation is not set
                $theme_location = has_nav_menu( 'mobile-navigation' ) ? 'mobile-navigation' : 'main-navigation';

                wp_nav_menu(array(
                    'theme_location'  => $theme_location,
                    'container'       => '',
                    'container_class' => '',
                    'menu_class'      => '',
                    'menu_id'         => '',
                    'fallback_cb'     => 'top_navigation_fallback',
                    'link_before'     => '<span>',
                    'link_after'      => '</span>',
                    'walker'          => new CyberstoreMikadoMobileNavigationWalker()
                )); ?>
            </nav>
        </div>
        <div class="mkd-mobile-widget-area">
            <?php if(is_active_sidebar('mkd-mobile-area')) : ?>
                <?php dynamic_sidebar('mkd-mobile-area'); ?>
            <?php endif; ?>
        </div>
    </div>
<?php }?>

<?php do_action('cyberstore_mikado_after_mobile_navigation'); ?>