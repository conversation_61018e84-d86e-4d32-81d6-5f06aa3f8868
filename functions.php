<?php

/*** Child Theme Function  ***/

if ( ! function_exists( 'cyberstore_mikado_child_theme_enqueue_scripts' ) ) {
	function cyberstore_mikado_child_theme_enqueue_scripts() {

		$parent_style = 'cyberstore-mikado-default-style';

		wp_enqueue_style( 'cyberstore-mikado-child-style', get_stylesheet_directory_uri() . '/style.css', array( $parent_style ) );
	}

	add_action( 'wp_enqueue_scripts', 'cyberstore_mikado_child_theme_enqueue_scripts' );
}
// BEGIN ENQUEUE PARENT ACTION
// AUTO GENERATED - Do not modify or remove comment markers above or below:

if ( !function_exists( 'chld_thm_cfg_locale_css' ) ):
    function chld_thm_cfg_locale_css( $uri ){
        if ( empty( $uri ) && is_rtl() && file_exists( get_template_directory() . '/rtl.css' ) )
            $uri = get_template_directory_uri() . '/rtl.css';
        return $uri;
    }
endif;
add_filter( 'locale_stylesheet_uri', 'chld_thm_cfg_locale_css' );

if ( !function_exists( 'chld_thm_cfg_parent_css' ) ):
    function chld_thm_cfg_parent_css() {
        wp_enqueue_style( 'chld_thm_cfg_parent', trailingslashit( get_template_directory_uri() ) . 'style.css', array(  ) );
    }
endif;
add_action( 'wp_enqueue_scripts', 'chld_thm_cfg_parent_css', 10 );
         
if ( !function_exists( 'child_theme_configurator_css' ) ):
    function child_theme_configurator_css() {
        wp_enqueue_style( 'chld_thm_cfg_child', trailingslashit( get_stylesheet_directory_uri() ) . 'style.css', array( 'chld_thm_cfg_parent' ) );
    }
endif;
add_action( 'wp_enqueue_scripts', 'child_theme_configurator_css', 10 );

// END ENQUEUE PARENT ACTION

function mytheme_enqueue_styles() {
    // Main stylesheet
    wp_enqueue_style( 'mytheme-style', get_stylesheet_uri() );

    // Google Fonts: Nosifer
    wp_enqueue_style( 'google-fonts-nosifer', 'https://fonts.googleapis.com/css2?family=Nosifer&display=swap', array(), null );
}
add_action( 'wp_enqueue_scripts', 'mytheme_enqueue_styles' );


function custom_enqueue_fonts() {
    wp_enqueue_style( 
        'figtree-font', 
        'https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700&display=swap', 
        array(), 
        null 
    );
}
add_action( 'wp_enqueue_scripts', 'custom_enqueue_fonts' );


// Keep only the first Product Category / Tag link in loops & archives
function cw_keep_only_first_term_link( $links ) {
    if ( is_singular( 'product' ) ) {
        return $links; // leave single product pages alone
    }
    return array_slice( (array) $links, 0, 1 );
}
add_filter( 'term_links-product_cat', 'cw_keep_only_first_term_link', 10 );
add_filter( 'term_links-product_tag', 'cw_keep_only_first_term_link', 10 );

// Ensure single product pages use the correct template
function cw_single_product_template( $template ) {
    if ( is_singular( 'product' ) ) {
        $single_product_template = locate_template( array( 'woocommerce/single-product.php' ) );
        if ( $single_product_template ) {
            return $single_product_template;
        }
    }
    return $template;
}
add_filter( 'template_include', 'cw_single_product_template', 99 );

// ========================================
// NEWSLETTER BACKGROUND IMAGE CUSTOMIZER
// ========================================

/**
 * Add customizer settings for newsletter background image
 */
function cw_newsletter_customizer_settings( $wp_customize ) {
    // Add Newsletter Section
    $wp_customize->add_section( 'cw_newsletter_section', array(
        'title'       => __( 'Newsletter Section', 'cyberstore' ),
        'description' => __( 'Customize the newsletter signup section appearance', 'cyberstore' ),
        'priority'    => 120,
    ) );

    // Newsletter Background Image Setting
    $wp_customize->add_setting( 'cw_newsletter_bg_image', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
        'transport'         => 'refresh',
    ) );

    // Newsletter Background Image Control
    $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'cw_newsletter_bg_image', array(
        'label'       => __( 'Newsletter Background Image', 'cyberstore' ),
        'description' => __( 'Upload an image for the newsletter section background (recommended: 400x300px)', 'cyberstore' ),
        'section'     => 'cw_newsletter_section',
        'settings'    => 'cw_newsletter_bg_image',
    ) ) );

    // Newsletter Enable/Disable Setting
    $wp_customize->add_setting( 'cw_newsletter_enabled', array(
        'default'           => true,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport'         => 'refresh',
    ) );

    // Newsletter Enable/Disable Control
    $wp_customize->add_control( 'cw_newsletter_enabled', array(
        'label'       => __( 'Enable Newsletter Section', 'cyberstore' ),
        'description' => __( 'Show or hide the newsletter signup section', 'cyberstore' ),
        'section'     => 'cw_newsletter_section',
        'type'        => 'checkbox',
    ) );

    // Newsletter Title Setting
    $wp_customize->add_setting( 'cw_newsletter_title', array(
        'default'           => "Don't Miss a Thing!",
        'sanitize_callback' => 'sanitize_text_field',
        'transport'         => 'refresh',
    ) );

    // Newsletter Title Control
    $wp_customize->add_control( 'cw_newsletter_title', array(
        'label'       => __( 'Newsletter Title', 'cyberstore' ),
        'description' => __( 'Main headline for the newsletter section', 'cyberstore' ),
        'section'     => 'cw_newsletter_section',
        'type'        => 'text',
    ) );

    // Newsletter Description Setting
    $wp_customize->add_setting( 'cw_newsletter_description', array(
        'default'           => 'Be the first to know about new arrivals, exclusive events, and all things comic culture.<br>Join our newsletter and stay in the loop with the latest from Comic Warehouse!',
        'sanitize_callback' => 'wp_kses_post',
        'transport'         => 'refresh',
    ) );

    // Newsletter Description Control
    $wp_customize->add_control( 'cw_newsletter_description', array(
        'label'       => __( 'Newsletter Description', 'cyberstore' ),
        'description' => __( 'Description text for the newsletter section (HTML allowed)', 'cyberstore' ),
        'section'     => 'cw_newsletter_section',
        'type'        => 'textarea',
    ) );
}
add_action( 'customize_register', 'cw_newsletter_customizer_settings' );

/**
 * Get newsletter background image URL
 */
function cw_get_newsletter_bg_image() {
    $image_url = get_theme_mod( 'cw_newsletter_bg_image', '' );
    return $image_url;
}

/**
 * Check if newsletter section is enabled
 */
function cw_is_newsletter_enabled() {
    return get_theme_mod( 'cw_newsletter_enabled', true );
}

/**
 * Get newsletter title
 */
function cw_get_newsletter_title() {
    return get_theme_mod( 'cw_newsletter_title', "Don't Miss a Thing!" );
}

/**
 * Get newsletter description
 */
function cw_get_newsletter_description() {
    return get_theme_mod( 'cw_newsletter_description', 'Be the first to know about new arrivals, exclusive events, and all things comic culture.<br>Join our newsletter and stay in the loop with the latest from Comic Warehouse!' );
}

/**
 * Add admin notice for newsletter customizer
 */
function cw_newsletter_admin_notice() {
    $screen = get_current_screen();
    if ( $screen->id === 'themes' ) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>Newsletter Section Added!</strong> You can now customize the newsletter background image and content by going to <strong>Appearance → Customize → Newsletter Section</strong>.</p>';
        echo '</div>';
    }
}
add_action( 'admin_notices', 'cw_newsletter_admin_notice' );


