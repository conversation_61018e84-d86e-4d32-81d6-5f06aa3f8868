<?php
$image_meta = get_post_meta(get_the_ID(), 'mkd_blog_list_featured_image_meta', true);
$has_featured = !empty($image_meta) || has_post_thumbnail();
?>
<article id="post-<?php the_ID(); ?>" <?php post_class($post_classes); ?>>
    <div class="mkd-post-content">
        <div class="mkd-post-info-left">
            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/author', 'blog', '', $part_params); ?>
            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/category', 'blog', '', $part_params); ?>
            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/share', 'blog', '', $part_params); ?>
        </div>
        <div class="mkd-post-content-inner">
            <?php if ($has_featured) : ?>
                <div class="mkd-post-heading">
                    <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/date-on-image', 'blog', '', $part_params); ?>
                    <?php cyberstore_mikado_get_module_template_part('templates/parts/image', 'blog', '', $part_params); ?>
                </div>
            <?php endif; ?>
            <div class="mkd-post-text">
                <div class="mkd-post-text-inner">
                    <div class="mkd-post-text-main">
                        <?php cyberstore_mikado_get_module_template_part('templates/parts/title', 'blog', '', $part_params); ?>
                        <?php cyberstore_mikado_get_module_template_part('templates/parts/excerpt', 'blog', '', $part_params); ?>
                        <?php do_action('cyberstore_mikado_single_link_pages'); ?>
                        <div class="mkd-post-info-top">
                            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/date', 'blog', '', $part_params); ?>
                            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/like', 'blog', '', $part_params); ?>
                            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/comments', 'blog', '', $part_params); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>

