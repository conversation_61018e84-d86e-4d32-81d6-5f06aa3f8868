<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
    <div class="mkd-post-content">
        <div class="mkd-post-info-left">
            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/author', 'blog', '', $part_params); ?>
            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/category', 'blog', '', $part_params); ?>
            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/share', 'blog', '', $part_params); ?>
        </div>
        <div class="mkd-post-content-inner">
            <div class="mkd-post-text">
                <div class="mkd-post-text-inner">
                    <div class="mkd-post-text-main">
                        <div class="mkd-post-quote-heading">
                            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-type/quote', 'blog', '', $part_params); ?>
                            <div class="mkd-post-mark">
                                <span class="mkd-icon-font-elegant icon_quotations mkd-quote-mark"></span>
                            </div>
                        </div>
                        <div class="mkd-post-info-top">
                            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/date', 'blog', '', $part_params); ?>
                            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/like', 'blog', '', $part_params); ?>
                            <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/comments', 'blog', '', $part_params); ?>
                        </div>
                        <?php the_content(); ?>
                        <?php do_action('cyberstore_mikado_single_link_pages'); ?>
                    </div>

                    <div class="mkd-post-info-bottom">
                        <h4 class="mkd-tag-title"><?php echo esc_html__('Tags:','cyberstore'); ?></h4>
                        <?php cyberstore_mikado_get_module_template_part('templates/parts/post-info/tags', 'blog', '', $part_params); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>
