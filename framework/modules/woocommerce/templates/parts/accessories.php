<div class="mkd-grid-row mkd-cross-sells-accessories-tab">
    <div class="mkd-grid-col-9 mkd-cross-sells">
        <?php

        global $woocommerce;

        $crosssellProductIds = get_post_meta(get_the_ID(), '_crosssell_ids');

        array_unshift($crosssellProductIds[0], get_the_ID());

        $queryArray = array(
            'post_status'         => 'publish',
            'post_type'           => 'product',
            'post__in'            => $crosssellProductIds[0],
            'ignore_sticky_posts' => 1,
            'posts_per_page'      => -1,
            'orderby'             => 'post__in'
        );

        $query_result = new \WP_Query($queryArray);

        if ($query_result->have_posts()) :

            woocommerce_product_loop_start();

            while ($query_result->have_posts()) : $query_result->the_post();

                wc_get_template_part('content', 'product');
            endwhile; // end of the loop.

            woocommerce_product_loop_end();

        endif;

        wp_reset_postdata();

        ?>
    </div>
    <div class="mkd-grid-col-3 mkd-accessories-cart-holder">
        <form class="cart" method="post" enctype='multipart/form-data'>
            <input type="hidden" name="mkd-multi-add" value="yes"/>
            <div class="mkd-product-accessories-list">
            <?php
                $_pf = new WC_Product_Factory();
                $_disabled = 'disabled';
                $total_price = 0;

                foreach ($crosssellProductIds[0] as $key => $id) {
                    $_product = $_pf->get_product($id);

                    $total_price += floatval($_product->get_price());
                    ?>
                    <div class="mkd-product-item">
                        <input <?php echo esc_attr($_disabled); ?> checked type="checkbox" class="mkd-product-check"
                                                          data-product_id="<?php echo esc_attr($id); ?>"
                                                          data-price="<?php echo esc_attr($_product->get_price()); ?>">
                        <input type="hidden" name="add-to-cart[<?php echo esc_attr( $id ); ?>]" value="1"/>
                        <div class="mkd-product-title-price">
                            <h5 class="mkd-product-title">
                                <?php if (!empty($_disabled)) {
                                    esc_html_e('This product: ', 'cyberstore');
                                }
                                print get_the_title($id); ?>
                            </h5>
                            <?php echo wp_kses_post($_product->get_price_html()); ?>
                        </div>
                    </div>
                    <?php
                    $_disabled = '';
                } ?>
            </div>
            <div class="mkd-total-price-holder clearfix">
                <h4 class="mkd-total-price-label"><?php esc_html_e('Total:', 'cyberstore'); ?></h4>

                <div class="mkd-total-price">
                    <h2 class="mkd-total-price-symbol-value">
                        <span class="mkd-total-price-symbol"><?php print get_woocommerce_currency_symbol(); ?></span><span
                            id="mkd-total-price-value" data-total-price="<?php echo esc_attr($total_price); ?>"><?php print number_format($total_price, 2); ?>
                    </h2>
                    <h6 class="mkd-total-price-items">
                        <span><?php esc_html_e('for ', 'cyberstore'); ?></span>
                        <span id="mkd-items-number"><?php print count($crosssellProductIds[0]); ?></span>
                        <span><?php esc_html_e(' items', 'cyberstore'); ?></span>
                    </h6>
                </div>
            </div>
            <div class="mkd-total-add-to-cart">
                <input class="button" type="submit" value="<?php esc_attr_e('Add To Cart', 'cyberstore'); ?>"/>
            </div>
        </form>
    </div>
</div>