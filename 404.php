<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
	<?php
	// Theme meta + head hooks
	do_action( 'cyberstore_mikado_header_meta' );
	wp_head();
	?>
	<style>
		
		/* 404 layout overrides */
		.mkd-404-page .mkd-content { background: #fff; }
		.mkd-404-wrap {
			min-height: 70vh;
			margin-top: 150px;
			display: flex; align-items: center; justify-content: center;
			text-align: center; padding: 48px 16px;
		}
		.mkd-404-card {
			max-width: 640px; margin: 0 auto;
		}
		.mkd-404-hero {
			width: 180px; height: 180px; margin: 0 auto 20px;
			border-radius: 50%; overflow: hidden;
		}
		.mkd-404-hero img { width: 100%; height: 100%; object-fit: cover; display:block; }
		.mkd-404-title {
			font-size: 40px; line-height: 1.15; margin: 8px 0 8px;
			font-weight: 800; color: #6B4DF6; /* purple headline */
		}
		.mkd-404-subtitle {
			font-size: 18px; font-weight: 700; color: #2c2c2c; margin: 0 0 12px;
		}
		.mkd-404-text {
			color: #6b7280; font-size: 14px; line-height: 1.7; margin: 0 auto 22px; max-width: 520px;
		}
		.mkd-404-btn {
			display: inline-flex; align-items: center; justify-content: center;
			padding: 10px 18px; border-radius: 999px; font-weight: 700;
			background: #2c2c2c; color: #fff; text-decoration: none;
			transition: transform .06s ease, opacity .2s ease;
		}
		.mkd-404-btn:hover { opacity: .95; transform: translateY(-1px); }
		/* Optional: hide site header on 404 if you want the ultra-clean look */
		/* .mkd-404-page .mkd-page-header { display:none; } */
	</style>
</head>
<body <?php body_class(); ?> itemscope itemtype="http://schema.org/WebPage">
	<?php
	do_action( 'cyberstore_mikado_after_body_tag' );
	?>

	<div class="mkd-wrapper mkd-404-page">
		<div class="mkd-wrapper-inner">
			<?php cyberstore_mikado_get_header(); ?>

			<div class="mkd-content" <?php cyberstore_mikado_content_elem_style_attr(); ?>>
				<div class="mkd-content-inner">
					<div class="mkd-404-wrap">
						<div class="mkd-404-card">

							<?php
							// Use provided image; swap if your file name differs.
							$image_url = '/wp-content/uploads/2025/08/The-Plot-Thickens….png';
							?>
							<div class="mkd-404-hero">
								<img src="<?php echo esc_url( $image_url ); ?>"
								     alt="<?php esc_attr_e( '404 Mascot', 'cyberstore' ); ?>">
							</div>

							<h1 class="mkd-404-title"><?php echo esc_html__( 'The Plot Thickens…', 'cyberstore' ); ?></h1>
							<h2 class="mkd-404-subtitle"><?php echo esc_html__( '404 - Page Not Found', 'cyberstore' ); ?></h2>

							<p class="mkd-404-text">
								<?php
								echo esc_html__(
									"This page pulled a disappearing act worthy of a secret identity. But your comic quest isn’t over yet! Suit up, and let’s get you back on track.",
									'cyberstore'
								);
								?>
							</p>

							<a class="mkd-404-btn" href="<?php echo esc_url( home_url( '/' ) ); ?>">
								<?php echo esc_html__( 'Go to Homepage', 'cyberstore' ); ?>
							</a>

						</div><!-- .mkd-404-card -->
					</div><!-- .mkd-404-wrap -->
				</div><!-- .mkd-content-inner -->
			</div><!-- .mkd-content -->
		</div><!-- .mkd-wrapper-inner -->
	</div><!-- .mkd-wrapper -->

	<?php get_footer(); ?>
</body>
</html>
