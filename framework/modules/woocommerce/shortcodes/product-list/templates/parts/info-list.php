<?php

$item_classes = $this_object->getItemClasses($params);
$text_wrapper_styles = $this_object->getTextWrapperStyles($params);
$params['title_styles'] = $this_object->getTitleStyles($params);
?>
<div class="mkd-pli mkd-pli-list-layout mkd-item-space <?php echo esc_attr($item_classes); ?>">
    <div class="mkd-pli-holder">
        <div class="mkd-pli-inner">
            <div class="mkd-pli-image">
                <?php cyberstore_mikado_get_module_template_part('templates/parts/image', 'woocommerce', '', $params); ?>
            </div>
            <a class="mkd-pli-link" itemprop="url" href="<?php the_permalink(); ?>"
               title="<?php the_title_attribute(); ?>"></a>
        </div>

        <div class="mkd-pli-text-wrapper" <?php echo cyberstore_mikado_get_inline_style($text_wrapper_styles); ?>>

            <?php

            cyberstore_mikado_get_module_template_part('templates/parts/category', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/title', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/rating', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/excerpt', 'woocommerce', '', $params);

            ?>
        </div>

        <div class="mkd-pli-cart-wrapper">

            <?php

            cyberstore_mikado_get_module_template_part('templates/parts/stock-availability', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/price', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/add-to-cart', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/compare', 'woocommerce', '', $params);

            cyberstore_mikado_get_module_template_part('templates/parts/wishlist', 'woocommerce', '', $params);

            ?>

        </div>
    </div>
</div>