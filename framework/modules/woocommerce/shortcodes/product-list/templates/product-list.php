<div
    class="mkd-pl-holder <?php echo esc_attr($holder_classes) ?>" <?php echo wp_kses($holder_data, array('data')); ?>>

    <?php
    if ($query_result->have_posts()):

        if (!empty($filters_position) && $filters_position == 'left') { ?>

            <div class="mkd-pl-filters">
                <div class="mkd-pl-filters-inner">

                    <?php
                    echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/layouts-filter', 'product-list', '', $params);
                    echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/categories-filter', 'product-list', '', $params);
                    echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/ordering-filter', 'product-list', '', $params);
                    ?>

                </div>

                <div class="mkd-product-list-sidebar-holder">
                    <?php cyberstore_mikado_get_product_list_widget_area(); ?>
                </div>

            </div>

        <?php } elseif (!empty($filters_position) && $filters_position == 'top') { ?>

            <div class="mkd-pl-filters">

                <?php if ($show_ordering_filter == 'yes' || $show_layouts_filter == 'yes') { ?>

                    <div class="mkd-pl-layouts-order-holder">

                        <?php
                        echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/layouts-filter', 'product-list', '', $params);
                        echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/ordering-filter', 'product-list', '', $params);
                        ?>

                    </div>

                <?php } ?>

                <?php echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/categories-filter', 'product-list', '', $params); ?>

            </div>

        <?php } ?>

        <div class="mkd-pl-outer-holder">

            <?php if ($show_category_filter == 'yes' || $show_ordering_filter == 'yes') {
                echo cyberstore_mikado_loading_spinner_rotate_circles();
            } ?>

            <div class="mkd-pl-outer mkd-outer-space clearfix">

                <?php
                while ($query_result->have_posts()) : $query_result->the_post();
                    echo cyberstore_mikado_get_woo_shortcode_module_template_part('templates/parts/info-' . $params['layout'], 'product-list', '', $params);
                endwhile;
                ?>
            </div>
        </div>
    <?php
    else:
        cyberstore_mikado_get_module_template_part('templates/parts/no-posts', 'woocommerce', '', $params);
    endif;
    wp_reset_postdata();
    ?>
</div>