<div <?php cyberstore_mikado_class_attribute($classes['mkd-product-search-holder']) ?>>
    <form class="mkd-product-search-form" action="<?php echo esc_url(home_url('/')); ?>" method="GET">

		 <input type="text" value="" class="mkd-product-search" name="s"
               placeholder="<?php esc_attr_e('Search', 'cyberstore'); ?>">
		
		
        <div class="mkd-product-category-holder">
            <select class="mkd-product-category" name="product_cat">
                <option value=""><?php esc_html_e('All Categories', 'cyberstore'); ?></option>

                <?php if (is_array($categories) && count($categories)) :
                    foreach ($categories as $category) : ?>

                        <option
                            value="<?php echo esc_attr($category->slug); ?>"><?php echo esc_html($category->name); ?>
                        </option>

                    <?php endforeach;
                endif; ?>

            </select>
        </div>


        <button type="submit" class="mkd-product-search-submit">
            <?php echo cyberstore_mikado_icon_collections()->renderIcon('dripicons-search', 'dripicons'); ?>
        </button>

        <input type="hidden" class="mkd-product-post-type" name="post_type" value="product">
    </form>
</div>