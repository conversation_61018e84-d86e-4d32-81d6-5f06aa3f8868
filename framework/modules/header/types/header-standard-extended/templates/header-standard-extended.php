<?php do_action('cyberstore_mikado_before_page_header'); ?>

    <header class="mkd-page-header">
        <?php do_action('cyberstore_mikado_after_page_header_html_open'); ?>

        <div class="mkd-logo-area">
            <?php do_action('cyberstore_mikado_after_header_logo_area_html_open'); ?>

            <?php if ($logo_area_in_grid) : ?>
            <div class="mkd-grid">
                <?php endif; ?>

                <div class="mkd-vertical-align-containers">
                    <div class="mkd-position-left"><!--
					 --><div class="mkd-position-left-inner">
                            <?php if (!$hide_logo) {
                                cyberstore_mikado_get_logo();
                            } ?>
                        </div>
                    </div>
                    <div class="mkd-position-right"><!--
					 --><div class="mkd-position-right-inner">
                            <?php cyberstore_mikado_get_header_widget_logo_area(); ?>
                        </div>
                    </div>
                </div>

                <?php if ($logo_area_in_grid) : ?>
            </div>
        <?php endif; ?>
        </div>

        <?php if ($show_fixed_wrapper) : ?>
        <div class="mkd-fixed-wrapper">
            <?php endif; ?>

            <?php if (!$disable_menu_area) : ?>
            <div class="mkd-menu-area">
                <?php do_action('cyberstore_mikado_after_header_menu_area_html_open'); ?>

                <?php if ($menu_area_in_grid) : ?>
                <div class="mkd-grid">
                    <?php endif; ?>

                    <div class="mkd-vertical-align-containers">
                        <?php cyberstore_mikado_get_extended_dropdown_menu(); ?>
                        <div class="mkd-position-left"><!--
					     --><div class="mkd-position-left-inner">
                                <?php cyberstore_mikado_get_main_menu(); ?>
                            </div>
                        </div>
                        <div class="mkd-position-right"><!--
					     --><div class="mkd-position-right-inner">
                                <?php cyberstore_mikado_get_header_widget_menu_area(); ?>
                            </div>
                        </div>
                    </div>

                    <?php if ($menu_area_in_grid) : ?>
                </div>
            <?php endif; ?>
            </div>
            <?php endif; ?>

            <?php if ($show_fixed_wrapper) { ?>
        </div>
    <?php } ?>

        <?php if ($show_sticky) {
            cyberstore_mikado_get_sticky_header();
        } ?>

        <?php do_action('cyberstore_mikado_before_page_header_html_close'); ?>
    </header>

<?php do_action('cyberstore_mikado_after_page_header'); ?>